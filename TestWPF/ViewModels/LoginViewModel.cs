using System;
using System.ComponentModel;
using System.Reactive;
using System.Reactive.Linq;
using System.Reactive.Subjects;
using System.Threading.Tasks;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using TestWPF.Extensions;
using CommunityToolkit.Mvvm.Input;

namespace TestWPF.ViewModels
{
    public partial class LoginViewModel : ObservableObject
    {
        // 用户名和密码
        
        private string _password = string.Empty;
        private string _statusMessage = "请输入用户名和密码";
        // 登录状态
        private bool _isLoading = false;
        // 登录按钮是否可用
        private bool _isLoginEnabled = false;

        // Reactive subjects for user input
        private readonly BehaviorSubject<string> _usernameSubject = new(string.Empty);
        private readonly BehaviorSubject<string> _passwordSubject = new(string.Empty);
        private readonly Subject<Unit> _loginSubject = new();

        public LoginViewModel()
        {
            InitializeReactiveStreams();
            LoginCommand = new RelayCommand(ExecuteLogin, () => IsLoginEnabled);
        }

        
        #region Properties

        [ObservableProperty] public string _username;
   

        public string Password
        {
            get => _password;
            set
            {
                if (_password != value)
                {
                    _password = value;
                    _passwordSubject.OnNext(value);
                    OnPropertyChanged();
                }
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                if (_statusMessage != value)
                {
                    _statusMessage = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                if (_isLoading != value)
                {
                    _isLoading = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsLoginEnabled
        {
            get => _isLoginEnabled;
            set
            {
                if (_isLoginEnabled != value)
                {
                    _isLoginEnabled = value;
                    OnPropertyChanged();
                    ((RelayCommand)LoginCommand).RaiseCanExecuteChanged();
                }
            }
        }

        public ICommand LoginCommand { get; }

        #endregion

        private void InitializeReactiveStreams()
        {
            // 组合用户名和密码的验证流
            var validationStream = Observable.CombineLatest(
                _usernameSubject.AsObservable(),
                _passwordSubject.AsObservable(),
                (username, password) => new { Username = username, Password = password })
                .Select(credentials => ValidateCredentials(credentials.Username, credentials.Password))
                .DistinctUntilChanged();

            // 订阅验证结果
            validationStream.Subscribe(result =>
            {
                IsLoginEnabled = result.IsValid && !IsLoading;
                if (!result.IsValid)
                {
                    StatusMessage = result.ErrorMessage;
                }
                else if (!IsLoading)
                {
                    StatusMessage = "准备登录";
                }
            });

            // 防抖处理 - 用户停止输入500ms后才进行验证
            var debouncedValidation = Observable.CombineLatest(
                _usernameSubject.AsObservable().Throttle(TimeSpan.FromMilliseconds(500)),
                _passwordSubject.AsObservable().Throttle(TimeSpan.FromMilliseconds(500)),
                (username, password) => new { Username = username, Password = password });

            debouncedValidation.Subscribe(credentials =>
            {
                if (!string.IsNullOrEmpty(credentials.Username) || !string.IsNullOrEmpty(credentials.Password))
                {
                    var result = ValidateCredentials(credentials.Username, credentials.Password);
                    if (result.IsValid)
                    {
                        StatusMessage = "输入有效，可以登录";
                    }
                }
            });

            
            // 登录流处理
            _loginSubject.AsObservable()
                .Where(_ => IsLoginEnabled)
                .Do(_ => 
                {
                    IsLoading = true;
                    IsLoginEnabled = false;
                    StatusMessage = "正在登录...";
                })
                .SelectMany(_ => PerformLoginAsync())
                .ObserveOnDispatcher()
                .Subscribe(
                    result =>
                    {
                        IsLoading = false;
                        if (result.Success)
                        {
                            StatusMessage = "登录成功！";
                        }
                        else
                        {
                            StatusMessage = result.ErrorMessage;
                            IsLoginEnabled = true;
                        }
                    },
                    error =>
                    {
                        IsLoading = false;
                        IsLoginEnabled = true;
                        StatusMessage = $"登录出错: {error.Message}";
                    });
        }

        private (bool IsValid, string ErrorMessage) ValidateCredentials(string username, string password)
        {
            if (string.IsNullOrWhiteSpace(username))
                return (false, "请输入用户名");

            if (string.IsNullOrWhiteSpace(password))
                return (false, "请输入密码");

            if (username.Length < 3)
                return (false, "用户名至少需要3个字符");

            if (password.Length < 6)
                return (false, "密码至少需要6个字符");

            return (true, string.Empty);
        }

        private async Task<(bool Success, string ErrorMessage)> PerformLoginAsync()
        {
            // 模拟网络请求延迟
            await Task.Delay(2000);

            // 模拟登录逻辑
            if (Username == "admin" && Password == "123456")
            {
                return (true, string.Empty);
            }
            else
            {
                return (false, "用户名或密码错误");
            }
        }

        private void ExecuteLogin()
        {
            _loginSubject.OnNext(Unit.Default);
        }

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion

        #region IDisposable

        public void Dispose()
        {
            _usernameSubject?.Dispose();
            _passwordSubject?.Dispose();
            _loginSubject?.Dispose();
        }

        #endregion
    }
}
