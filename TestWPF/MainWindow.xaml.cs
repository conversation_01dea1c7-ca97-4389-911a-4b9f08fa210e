using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using TestWPF.ViewModels;

namespace TestWPF;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private LoginViewModel _viewModel;

    public MainWindow()
    {
        InitializeComponent();

        // 初始化ViewModel
        _viewModel = new LoginViewModel();
        DataContext = _viewModel;

        // 处理密码框的绑定（PasswordBox不支持直接绑定）
        PasswordBox.PasswordChanged += (s, e) =>
        {
            _viewModel.Password = PasswordBox.Password;
        };

        // 设置焦点
        Loaded += (s, e) => UsernameTextBox.Focus();

        // 处理回车键登录
        KeyDown += (s, e) =>
        {
            if (e.Key == Key.Enter && _viewModel.IsLoginEnabled)
            {
                _viewModel.LoginCommand.Execute(null);
            }
        };
    }

    protected override void OnClosed(EventArgs e)
    {
        _viewModel?.Dispose();
        base.OnClosed(e);
    }
}