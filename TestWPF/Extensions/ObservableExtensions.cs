using System;
using System.Reactive.Concurrency;
using System.Reactive.Linq;
using System.Threading;

namespace TestWPF.Extensions
{
    public static class ObservableExtensions
    {
        /// <summary>
        /// 在WPF UI线程上观察Observable序列
        /// </summary>
        public static IObservable<T> ObserveOnDispatcher<T>(this IObservable<T> source)
        {
            return source.ObserveOn(SynchronizationContext.Current);
        }
    }
}
